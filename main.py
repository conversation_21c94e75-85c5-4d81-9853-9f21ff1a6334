












































# def hello():
#     return "Hello how are you World"


# print(hello())






















































# def pall_str(st):

#     compare_str = ""

#     for i in range(len(st)-1,-1,-1):
#         compare_str += st[i]

#     if compare_str == st:
#         return True
#     else:
#         return False



# print(pall_str("namsan"))
















































# def hello(name,age):
#     print(f"Your name is: {name}, your age is {age}")







# hello(age=33,name="<PERSON>")




















# def sum(a,b=45):
#     print(f"The sum of your numbers is: {a+b}")


# sum(12,12)
# # sum(45,45)





















# def hello():
#     print("This is Hello function so i am doing hello")


# hello()









































# import random

# ran_num = random.randint(1,10)

# tries = 0


# #print(ran_num)

# while True:
#     guess = int(input("Guess a number: "))


#     if guess == ran_num:
#         print("You win")
#         tries += 1
#         break
#     elif ran_num < guess:
#         print("Go little lower")
#         tries += 1
#     elif ran_num > guess:
#         print("Go little higher")
#         tries += 1
#     else:
#         print("You are wrong")
#         tries += 1
#         # if tries == 3:
#         #     print("You lose")
#         #     break




































# a = int(input("Enter a number: "))


# org = a
# rev = 0

# while a > 0:
#     rev = rev * 10 + a % 10
#     a //= 10


# if org == rev:
#     print("Palindrome")
# else:
#     print("Not Palindrome")



































# a = int(input("Enter a number: "))

# rev = 0

# while a > 0:
#     rev = rev * 10 + a % 10
#     a //= 10



# print(rev)









































# a = 1

# while a <= 30:
#     print(a)
#     a += 1




























# a = input("Enter the name: ")

# isalpha_count = 0
# isdigit_count = 0
# isascii_count = 0

# for i in a:

#     if i.isalpha():
#         print(i)
#         isalpha_count += 1
#     elif i.isdigit():
#         print(i)
#         isdigit_count += 1
#     elif i.isascii():
#         print(i)    
#         isascii_count += 1


# print(f"Alpha count: {isalpha_count}")
# print(f"Digit count: {isdigit_count}")
# print(f"ASCII count: {isascii_count}")
        


















# a = input("Enter the name: ")



# b = ""

# for i in range(len(a)-1,-1,-1):
#     b += a[i]
#     print(a[i])

# if b == a:
#     print("Palindrome")
# else:
#     print("Not Palindrome")
































# factor_sum = int(input("Enter a number: "))

# sum_of_factors = 0
# count = 0

# for i in range(1,factor_sum+1):
#     if factor_sum % i == 0:
#         print(f"{factor_sum} X {i} = {factor_sum*i}")
#         sum_of_factors += i
#         count += 1

#         if count > 2:            
#             break
#         # if sum_of_factors == factor_sum:
#         #     break





# if count <= 2:
#     print(f"Sum of factors of {factor_sum} is {sum_of_factors}")









































# factor_no_a_number = int(input("Enter a number: "))


# for i in range(1,factor_no_a_number+1):

#     if factor_no_a_number % i == 0:
#         print(f"{factor_no_a_number} X {i} = {factor_no_a_number*i}")



# # n = int(input("Enter the number for sum of even and odd number in range: "))

# # even_sum = 0
# # odd_sum = 0

# # for i in range(1,n+1):
# #     if i % 2 == 0:
# #         even_sum += i
# #     else:
# #         odd_sum += i

# # print("Sum of even numbers:", even_sum)
# # print("Sum of odd numbers:", odd_sum)













































# sum_upto_n_terms = int(input("Enter the number of fact: "))

# sum = 1

# for i in range(1,sum_upto_n_terms+1):
#     sum *= i
    
# print(sum)




























# sum_upto_n_terms = int(input("Enter the number of terms: "))

# sum = 0

# for i in range(1,sum_upto_n_terms+1):
#     sum += i
    
# print(sum)



# # table = int(input("Enter table number: "))

# # for i in range(table,(table*10)+1, table):
# #     print(f"{table} X {i//table} = {i}")




















# n_number = int(input("Please enter the number of time you want to print: "))

# for i in range(n_number,0,-1):
#     print(i)









































# n_time = int(input("Please enter the number of time you want to print: "))

# for i in range(n_time):
#     print("Hello World - ",i+1)







































# for i in range(1,21,1):
#     if i == 151:
#         print("break statment is executed")
#         break
#     print(i)
# else:
#     print("break statment not executed")

#     # else:
#     #     print(i)



# # a = "SHERYIANS IS COOL"

# # for i in a:
# #     print(i)































# a = "SHERYIANS TEACHES INDUSTRY THINGS"

# print(len(a))

# for i in range(len(a)):
#     print(a[i])






















































# table = int(input("Enter table number: "))


# for i in range(table,(table*10)+1,table):
#     print(i)





# a = range(-3,-16,-1)


# for i in a:
#     print(i)













# for i in range(100):
#     print("Hello World")







































# temp = int(input("Enter temperature: "))

# if temp <=0:
#     print("Freezing Cold")
# elif temp > 0 and temp <= 10:
#     print("Very Cold")
# elif temp > 10 and temp <= 20:
#     print("Cold")
# elif temp > 20 and temp <= 30:
#     print("Normal")
# elif temp > 30 and temp <= 40:
#     print("Hot")
# else:
#     print("Very Hot")













































# year = int(input("Please enter the year: "))

# if year % 100 == 0:
#     if year % 400 == 0:
#         print("Leap year - 1")
#     else:
#         print("Not leap year")
# else:
#     if year % 4 == 0:
#         print("Leap year - 2")
#     else:
#         print("Not leap year")

















































# name = input("Please tell you name: ")
# age = int(input("Please tell you age: "))


# if age >= 18:
#     print("You are eligible for vote")
# else:
#     print(f"You are not eligible for vote, but you can voke after {18-age} years")























































# odd_even = int(input("Enter a number: "))

# if odd_even % 2 == 0:
#     print("Even")
# else:
#     print("Odd")


















































# gen = input("Enter your gender: ")

# if gen == 'M':
#     print("Good morning sir")
# elif gen == 'F':
#     print("Good morning ma'am")
# else:
#     print("Good morning")
















































# num1 = int(input("Enter first number: "))
# num2 = int(input("Enter secone number: "))



# if num1>num2:
#     print(f"{num1} is greater than {num2}")
# elif num2>num1:
#     print(f"{num2} is greater than {num1}")
# else:
#     print(f"{num1} are same {num2}")
























































# a = 9

# if a > 10:
#     print("a is greater than 10")
# else:
#     print("a is less than 10")





# money = int(input("Enter your money: "))

# if money == 10:
#     print("I will have chocobar")
# elif money == 20:
#     print("I will have mongo")
# elif money == 30:
#     print("I will have forsty")
# else:
#     print("I will have cone")
    










































# print(126 > 130)


# print( (456==456) != (235 == 236) )



# print(12 < 10 or 45 == 56 or 69 > 70 or 15!=13 )

# print(True and bool(0))



# # print (12 > 20 and 122 > 10 and 34 == 34 and 45 < 90)

# # print(12 != 12 or 23 == 45 or 67 == 56 or 10 > 5)

# # print(not 12 == 12)












































# print(ord("A"))
# print(ord("B")) 

# print("A" < "B")


















































# a = 12.1
# b = 12

# print(a == b)

# print(a != b)

# print (a > b)


































































# a = 23



# a = 20

# a += 20

# a += 40

# a += 60

# print(a)

# a -= 100

# print(a)

# a *= 2

# print(a)

# a /= 4

# print(a)

# a //= 4

# print(a)


# a **= 2

# print(a)


# a %= 4

# print(a)






















































# print(5*4/2+3-1)





















































# a = 5
# b = 32

# print(a+b)
# print(b-a)
# print(a*b)

# print(b//a)
# print(b/a)

# print(5**100)

# print(32%5)























































# name = "Rakesh"
# age = int(input("What is your age: "))

# # print("My name is ",name," and my age is ",age)

# print(type(age))

# print(f"My name is {name}, and my age is {age}")
































































# a = -10

# a = bool(a)

# print(type(a))

# print(a)
























































"""hello i am rakesh
dshhss
sshhss
shsshshs"""

# print("hello i am rakesh")

# rakeshVerma = "rakesh verma"
# print(rakeshVerma)

# RakeshVerma = "Rakesh Verma"
# print(RakeshVerma)

# rakesh_verma = "rakesh_verma"
# print(rakesh_verma)

# a = -23

# print(type(a))

# b = 56.8

# print(type(b))

# c = 12/3

# print(c)
# print(type(c))


# v = 34j

# print(v)
# print(type(v))

# st = 'this is test'

# print(st)  
# print(type(st))


# b = True

# print(b)
# print(type(b))

# t = False

# print(t)
# print(type(t))  

