"""hello i am rakesh
dshhss
sshhss
shsshshs"""

# print("hello i am rakesh")

# rakeshVerma = "rakesh verma"
# print(rakeshVerma)

# RakeshVerma = "Rakesh Verma"
# print(RakeshVerma)

# rakesh_verma = "rakesh_verma"
# print(rakesh_verma)

a = -23

print(type(a))

b = 56.8

print(type(b))

c = 12/3

print(c)
print(type(c))


v = 34j

print(v)
print(type(v))

# Complex Data Types Examples

# 1. List - ordered, mutable collection
my_list = [1, 2, 3, "hello", True, 3.14]
print("\nList:")
print(my_list)
print(type(my_list))

# 2. Tuple - ordered, immutable collection
my_tuple = (1, 2, 3, "world", False)
print("\nTuple:")
print(my_tuple)
print(type(my_tuple))

# 3. Dictionary - key-value pairs, mutable
my_dict = {
    "name": "<PERSON><PERSON><PERSON>",
    "age": 25,
    "city": "Mumbai",
    "skills": ["Python", "Java", "SQL"]
}
print("\nDictionary:")
print(my_dict)
print(type(my_dict))

# 4. Set - unordered collection of unique elements
my_set = {1, 2, 3, 4, 4, 5}  # Note: duplicate 4 will be removed
print("\nSet:")
print(my_set)
print(type(my_set))

# 5. Nested structures - complex combinations
nested_data = {
    "students": [
        {"name": "Alice", "grades": [85, 90, 78]},
        {"name": "Bob", "grades": [92, 88, 95]},
        {"name": "Charlie", "grades": [76, 82, 89]}
    ],
    "class_info": {
        "subject": "Python Programming",
        "teacher": "Mr. Smith",
        "room": 101
    }
}
print("\nNested Data Structure:")
print(nested_data)
print(type(nested_data))

